# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Build and deploy to internal track for staging flavor"
  lane :staging do
    flutter(
      build: "appbundle",              # == flutter build appbundle
      build_args: ["--flavor", "staging", "-t", "lib/main_staging.dart"]
    )

    aab_path = lane_context[:FLUTTER_BUILD_OUTPUT_PATH]

    supply(
      track: "alpha",
      package_name: "com.wd.e2ee.chat.staging",
      aab: aab_path,
      skip_upload_changelogs: true,
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleRelease")
    upload_to_play_store
  end
end
